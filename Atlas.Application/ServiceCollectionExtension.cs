using Atlas.Application.Handlers;
using Atlas.Application.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Atlas.Application
{
    public static class ServiceCollectionExtension
    {
        public static void ConfigureApplication(this IServiceCollection services)
        {
            ConfigureServices(services);
        }

        private static void ConfigureServices(IServiceCollection services)
        {
            services.AddTransient<IHomeApplication, HomeApplication>();
            services.AddTransient<IProfileApplication, ProfileApplication>();
            services.AddTransient<IUserMentionApplication, UserMentionApplication>();
            services.AddTransient<IAnnouncementApplication, AnnouncementApplication>();
            services.AddTransient<IOAuthIntegrationApplication, OAuthIntegrationApplication>();
            services.AddTransient<IContentAttachmentApplication, ContentAttachmentApplication>();
            services.AddTransient<IMeetingApplication, MeetingApplication>();
            services.AddTransient<IWorkgroupApplication, WorkgroupApplication>();
            services.AddTransient<IWorkgroupUsersApplication, WorkgroupUsersApplication>();
            services.AddTransient<IWorkgroupOwnerApplication, WorkgroupOwnerApplication>();
            services.AddTransient<ITokenApplication, TokenApplication>();
            services.AddTransient<IAuthenticationApplication, AuthenticationApplication>();
            services.AddTransient<ITaskApplication, TaskApplication>();
            services.AddTransient<IFormApplication, FormApplication>();
            services.AddTransient<ISessionApplication, SessionApplication>();
            services.AddTransient<ISuperAdminClientApplication, SuperAdminClientApplication>();
            services.AddTransient<IPlanFeatureApplication, PlanFeatureApplication>();
            services.AddTransient<IActivityApplication, ActivityApplication>();
            services.AddTransient<IContentApplication, ContentApplication>();
            services.AddTransient<IContentPermissionApplication, ContentPermissionApplication>();
            services.AddTransient<IContentSubscriberApplication, ContentSubscriberApplication>();
            services.AddTransient<IContentOwnerApplication, ContentOwnerApplication>();
            services.AddTransient<IMeetingAgendaItemApplication, MeetingAgendaItemApplication>();
            services.AddTransient<IExternalUserApplication, ExternalUserApplication>();
            services.AddTransient<IExternalDocumentRequestApplication, ExternalDocumentRequestApplication>();
            services.AddTransient<IKnowledgeBaseApplication, KnowledgeBaseApplication>();
            services.AddTransient<IContentCommentApplication, ContentCommentApplication>();
            services.AddTransient<IExportApplication, ExportApplication>();
            services.AddTransient<IClientBackupApplication, ClientBackupApplication>();
            services.AddTransient<IBlueBookApplication, BlueBookApplication>();
            services.AddTransient<IResolutionApplication, ResolutionApplication>();
            services.AddTransient<IMeetingMinuteApplication, MeetingMinuteApplication>();
            services.AddTransient<INoteApplication, NoteApplication>();
        }
    }
}

