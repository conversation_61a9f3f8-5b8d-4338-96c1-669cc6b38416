using Atlas.Business.Helpers;
using Atlas.Business.ViewModels;
using Atlas.Business.ViewModels.Forms;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Form;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Dtos.Form;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Ganss.Xss;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SharpRaven;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class FormService
    {
        protected User _user;
        protected ContentService _contentService;
        protected ContentRepository _contentRepository;
        protected FormRepository _repo;
        private readonly ContentRequestFilter _filter;

        public FormService(User user)
        {
            this._user = user;
            this._contentService = new ContentService(user.userId);
            this._contentRepository = new ContentRepository(user.userId);
            this._repo = new FormRepository(this._user);

            this._filter = new ContentRequestFilter();
            this._filter.pageSize = 10;
            this._filter.pageNumber = 1;
        }

        public FormService()
        {

        }

        public async Task<PagedListFormViewModel> ListFormsOwnedByUser(int page, int[] workgroupIds)
        {
            ValidateUser();

            _filter.pageNumber = page <= 0 ? 1 : page;
            _filter.workgroups = workgroupIds;

            //returns all forms that user is an owner
            var list = await _contentService.GetOwnedForms(_filter);
            var count = await _contentService.CountOwnedFormsAsync(_filter);

            var result = new PagedListFormViewModel()
            {
                PageSize = _filter.pageSize.Value,
                PageNumber = _filter.pageNumber.Value,
                TotalCount = count,
                List = list
            };

            return result;
        }

        public async Task<PagedListFormViewModel> ListReceivedForms(int page, int[] workgroupIds, bool? answered)
        {
            ValidateUser();

            _filter.pageNumber = page <= 0 ? 1 : page;
            if (answered.HasValue)
            {
                _filter.status = answered.Value ? "FINISHED" : "PENDING";
            }

            _filter.workgroups = workgroupIds;

            //returns all forms that user has permission
            var list = await _contentService.GetReceivedForms(_filter);
            var count = await _contentService.CountReceivedFormsAsync(_filter);

            var result = new PagedListFormViewModel()
            {
                PageSize = _filter.pageSize.Value,
                PageNumber = _filter.pageNumber.Value,
                TotalCount = count,
                List = list
            };

            return result;
        }

        public async Task<bool> SaveFormBody
        (
            Guid contentUuid, 
            List<FormSection> sections, 
            bool publish, 
            string userAgent, 
            StorageSettings storageSettings
        )
        {
            // Content implicity validation
            //   - the user must have permission ✓
            //   - not deleted ✓
            bool isDraft = !publish;
            var content = await _contentService.GetSimpleContentAsync(contentUuid);

            ValidateSaveFormBody(content, sections, isDraft);

            var form = await _repo.GetSimpleForm(contentUuid);

            if (form is null)
            {
                throw new SecurityException("FORM_NOT_FOUND");
            }

            if (publish && form.expirationDate < DateTime.UtcNow)
            {
                throw new InvalidOperationException("EXPIRED_FORM_DEADLINE");
            }

            foreach (var section in sections)
            {
                section.formId = form.formId;
                section.formSectionId = 0; // to ensure that EF will treat it as a new section
            }

            var questions = sections.SelectMany(s => s.FormQuestions);

            if (questions.Any())
            {
                foreach (var question in questions)
                {
                    question.formQuestionId = 0; // to ensure that EF will treat it as a new question
                }
            }

            try
            {
                var result = (await _repo.SaveSections(form.formId, sections) > 0);

                if (result)
                {
                    if (isDraft)
                    {
                        new ContentActivityService(_user.userId).Add(form.contentId, form.contentUuid, new ContentActivity()
                        {
                            contentId = form.contentId,
                            date = DateTime.UtcNow,
                            type = "UPDATED",
                            activityUser = _user.userId
                        }, 
                        userAgent, 
                        storageSettings, 
                        groupTime: 5);
                    }
                    else if (publish)
                    {
                        await PublishFormAsync(content, form, userAgent, storageSettings);

                        new ContentActivityService(_user.userId).Add
                        (
                            form.contentId,
                            form.contentUuid,
                            new ContentActivity
                            {
                                date = DateTime.UtcNow,
                                type = Operations.FORM_REQUEST_ANSWER,
                                activityUser = _user.userId,
                                contentData = JsonConvert.SerializeObject(new
                                {
                                    title = content.title
                                })
                            },
                            userAgent,
                            storageSettings
                        );
                    }
                }

                return result;
            }
            catch (DbUpdateException ex)
            {
                if (ex.InnerException != null)
                {
                    throw new InvalidOperationException(ex.InnerException.Message);
                }

                return false;
            }
        }

        public async Task<bool> SaveFormAnswers
        (
            Guid contentUuid, 
            bool submit, 
            List<FormQuestionAnswerViewModel> questionAnswersViewModel,
            string userAgent,
            StorageSettings storageSettings
        )
        {
            Content content = await _contentService.GetSimpleContentAsync(contentUuid);
            await ValidateSaveFormAnswers(content);

            var respondent = await _repo.GetFormRespondent(contentUuid, _user.userId);
            if (respondent != null && respondent.status == "CLOSED")
                throw new InvalidOperationException("FORM_ALREADY_ANSWERED");

            Form form = await _repo.GetForm(contentUuid, false);

            IEnumerable<FormQuestionAnswerViewModel> validatedAnswers = ValidateSaveFormAnswers(form, submit, questionAnswersViewModel);

            List<FormQuestionResponse> answers = validatedAnswers.SelectMany(answer => answer.FormQuestionResponses).ToList();

            List<FormQuestionComment> comments = validatedAnswers.Select(comment => comment.FormQuestionComment)
                .Where(comment => comment != null)
                .ToList();

            foreach (var comment in comments) { comment.userId = _user.userId; }
            foreach (var answer in answers) { answer.userId = _user.userId; }

            bool iAmTheLastRespondent = false;

            if (submit)
                iAmTheLastRespondent = await CheckHowManyDidNotAnswer(content.contentId, _user.userId) == 1;

            var questionsIdList = validatedAnswers.Select(question => question.formQuestionId).ToList();
            bool successOnSaveAnswers = await _repo.SaveAnswers(_user.userId, content, respondent, questionsIdList, comments, answers, iAmTheLastRespondent, submit);

            if (submit && successOnSaveAnswers && !form.anonymousAnswer)
            {
                var contentActivityService = new ContentActivityService(_user.userId);
                contentActivityService.Add(content.contentId, content.contentUuid, new ContentActivity
                {
                    activityUser = _user.userId,
                    type = Operations.FORM_ANSWERED,
                    processed = true,
                    date = DateTime.UtcNow,
                    contentData = JsonConvert.SerializeObject(new { content.title, form.formId })
                }, userAgent, storageSettings);
            }

            if (submit && successOnSaveAnswers && iAmTheLastRespondent)
            {
                new ContentActivityService(_user.userId).Add(content.contentId, content.contentUuid, new ContentActivity
                {
                    date = DateTime.UtcNow,
                    type = Operations.FORM_CLOSED,
                    activityUser = _user.userId,
                    processed = false,
                    contentData = JsonConvert.SerializeObject(new { title = content.title })
                }, userAgent, storageSettings);
            }

            return successOnSaveAnswers;
        }

        public async Task<DuplicateFormViewModel> DuplicateForm(Content content, int copyFromForm)
        {
            this.ValidateUser();

            List<string> notifications = new List<string>();

            WorkgroupService workgroupService = new WorkgroupService(_user.userId);
            WorkgroupViewModel userWorkgroup = await workgroupService.Get(content.workgroupId, includeHomeData: false);

            if (!(await this.ValidateDuplicateForm(content, _user.userId, userWorkgroup, notifications)))
                return new DuplicateFormViewModel() { success = false, messages = notifications };

            var permissions = this.AddPermissions(content.ContentSubscriber, _user.userId);

            List<ContentOwner> owners = new List<ContentOwner>
            {
                new ContentOwner()
                {
                    userId = _user.userId,
                    createUser = _user.userId,
                    createDate = DateTime.UtcNow
                }
            };

            Content newContent = new Content()
            {
                createDate = DateTime.UtcNow,
                createUser = _user.userId,
                contentKey = Guid.NewGuid(),
                type = content.type,
                status = "OPEN",
                deleted = false,
                workgroupId = content.workgroupId,
                title = content.title,
                lastUpdate = DateTime.UtcNow,
                ContentPermission = permissions,
                ContentSubscriber = content.ContentSubscriber,
                ContentOwner = owners,
                Forms = content.Forms
            };

            Content contentModelo = await _contentRepository.GetContentFormWithWorkgroup(copyFromForm);
            Form formModelo = contentModelo?.Forms.FirstOrDefault();

            if (formModelo == null || contentModelo?.deleted == true)
                return new DuplicateFormViewModel() { success = false, messages = new List<string>() { "TRYING_TO_COPY_AN_INVALID_FORM" } };

            bool isOwnerOfContentModelo = contentModelo.ContentOwner.Any(co => co.userId == _user.userId);

            if (!isOwnerOfContentModelo)
                return new DuplicateFormViewModel() { success = false, messages = new List<string>() { "DO_NOT_HAVE_PERMISSIONS" } };

            notifications.Clear();

            List<FormSection> newSections = GenerateNewSections(formModelo);
            int contentId = await _repo.CreateContentAndDuplicateForm(newContent, newSections);

            if (contentId != -1)
            {
                new ContentActivityService(_user.userId).Add(contentId, new ContentActivity
                {
                    date = DateTime.UtcNow,
                    type = Operations.FORM_DUPLICATED,
                    activityUser = _user.userId,
                    processed = true,
                    contentData = JsonConvert.SerializeObject(new { contentId = contentModelo.contentId })
                });

                return new DuplicateFormViewModel()
                {
                    success = true,
                    messages = new List<string>() { "SUCCESS" },
                    id = contentId
                };
            }

            return new DuplicateFormViewModel() { success = false, messages = new List<string>() { "OPERATION_FAILED" } };
        }

        /// <summary>
        /// IMPORTANT: use this with caution as the business validations were done in method AddSections.
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private async Task<bool> PublishFormAsync(Content content, Form form, string userAgent, StorageSettings storageSettings)
        {
            var result = await _repo.PublishForm(content, form);

            if (result)
            {
                new ContentActivityService(_user.userId).Add
                (
                    content.contentId, 
                    content.contentUuid,
                    new ContentActivity
                    {
                        date = DateTime.UtcNow,
                        type = Operations.FORM_PUBLISHED,
                        activityUser = _user.userId,
                        processed = true,
                        contentData = JsonConvert.SerializeObject(new
                        {
                            title = content.title
                        })
                    }, 
                    userAgent,
                    storageSettings
                );
            }

            return result;
        }

        public async Task<bool> ToggleAnonymousAnswer(int contentId)
        {
            ValidateUser();
            // content validation
            var content = await _contentService.GetSimpleContentAsync(contentId);
            ValidateContent(content);

            if (!content.UAC.can_update_anonymous_answer_status)
                throw new SecurityException("INVALID_GRANT");

            if (await _repo.GetFormRespondentsTotal(contentId) > 0)
                throw new SecurityException("FORM_HAS_ANSWERS");

            //update anonymousAnswer
            var entries = await _repo.ToggleAnonymousAnswer(contentId);

            return entries == 1;
        }

        public async Task<bool> DeleteForm(int contentId)
        {
            Content content = await _contentService.GetSimpleContentAsync(contentId);

            this.ValidateDeleteForm(content);

            var result = await _contentService.Delete(contentId);
            return result;
        }

        private void ValidateDeleteForm(Content content)
        {
            ValidateUser();
            ValidateContent(content);

            if (!content.UAC.delete)
            {
                throw new SecurityException("INVALID_GRANT");
            }
        }

        private void ValidateSaveFormBody(Content content, IEnumerable<FormSection> sections, bool isDraft)
        {
            ValidateUser();
            ValidateContent(content);

            if (!content.UAC.can_change_form)
            {
                throw new SecurityException("INVALID_GRANT");
            }

            if (!sections.Any())
                throw new InvalidOperationException("INVALID_SECTION_LENGTH");

            foreach (FormSection section in sections)
            {
                if (string.IsNullOrEmpty(section.title))
                {
                    if (!isDraft)
                    {
                        throw new InvalidOperationException("INVALID_SECTION_TITLE");
                    }

                    section.title = "";
                }

                if (section.sectionOrder < 0)
                    throw new InvalidOperationException("INVALID_SECTION_ORDER");

                if ((section.FormQuestions?.Count ?? 0) == 0 && !isDraft)
                    throw new InvalidOperationException("INVALID_FORMQUESTIONS_LENGTH");

                ValidateFormQuestions(section, isDraft);
            }
        }

        private static void ValidateFormQuestions(FormSection section, bool isDraft)
        {
            foreach (FormQuestion question in section.FormQuestions)
            {
                if (string.IsNullOrEmpty(question.title))
                {
                    if (!isDraft)
                    {
                        throw new InvalidOperationException("INVALID_QUESTION_TITLE");
                    }

                    question.title = "";
                }

                if (question.questionOrder < 0)
                    throw new InvalidOperationException("INVALID_QUESTION_ORDER");

                if (question.type != "FREE" && question.type != "CLASSIFICATION" && question.type != "MULTIPLE")
                    throw new InvalidOperationException("INVALID_QUESTION_TYPE");

                int optionCount = question.FormQuestionOptions.Count;
                if (optionCount == 0 && !isDraft)
                {
                    throw new InvalidOperationException("QUESTION_WITHOUT_OPTIONS");
                }

                if (question.type == "CLASSIFICATION" && ((optionCount < 2 && !isDraft) || optionCount > 6) ||
                    question.type == "MULTIPLE" && optionCount > 15)
                {
                    throw new InvalidOperationException("INVALID_OPTIONS_COUNT");
                }

                if (question.type != "CLASSIFICATION" && question.type != "MULTIPLE" && question.FormQuestionOptions.Count > 0 && question.FormQuestionOptions.Any(fco => fco.requiredJustification))
                {
                    throw new InvalidOperationException("INVALID_JUSTIFICATION_QUESTION_TYPE");
                }

                ValidateQuestionsOptionsLength(question, isDraft);
            }
        }

        private static void ValidateQuestionsOptionsLength(FormQuestion question, bool isDraft)
        {
            if (!isDraft && question.type == "CLASSIFICATION")
            {
                foreach (var option in question.FormQuestionOptions)
                {
                    option.title = option.title.Substring(0, Math.Min(option.title.Length, 25));
                }
            }
        }

        private async Task<bool> ValidateSaveFormAnswers(Content content)
        {
            ValidateContent(content);
            ValidateUser();

            if (content.status != "PUBLISHED")
                throw new SecurityException("FORM_EITHER_REOPENED_OR_CLOSED");

            if (!await _repo.IsUserFormResponder(content.contentId))
                throw new SecurityException("USER_NOT_A_FORM_RESPONDER");

            return true;
        }

        private IEnumerable<FormQuestionAnswerViewModel> ValidateSaveFormAnswers(Form dbForm, bool submit, List<FormQuestionAnswerViewModel> answersViewModel)
        {
            if (!submit)
            {
                // ensure that exists only one sectionId, if its not submit scenario
                int sectionIdBase = answersViewModel[0].formSectionId;
                if (!answersViewModel.All(answer => answer.formSectionId == sectionIdBase))
                    throw new InvalidOperationException("INVALID_SECTION");
            }

            if (dbForm is null)
                throw new SecurityException("FORM_NOT_FOUND");

            var formSectionsIdList = answersViewModel.Select(answer => answer.formSectionId);
            IEnumerable<FormSection> dbSections = dbForm.FormSections.Where(section => formSectionsIdList.Contains(section.formSectionId));
            if (!dbSections.Any())
                throw new SecurityException("SECTION_NOT_FOUND");

            var formQuestionsIdList = answersViewModel.Select(answer => answer.formQuestionId);
            IEnumerable<FormQuestion> dbQuestions = dbSections.
                SelectMany(sec => sec.FormQuestions.Where(question => formQuestionsIdList.Contains(question.formQuestionId)));

            if (!dbQuestions.Any())
                throw new SecurityException("QUESTIONS_NOT_FOUND");

            var answers = answersViewModel.SelectMany(a => a.FormQuestionResponses);

            if (answers.Any(a => a.formQuestionOptionId == 0))
            {
                throw new InvalidOperationException("INVALID_OPTION");
            }

            var dbQuestionOptionIdList = dbQuestions.SelectMany(q => q.FormQuestionOptions.Select(o => o.formQuestionOptionId));

            if (!answers.All(a => dbQuestionOptionIdList.Contains(a.formQuestionOptionId)))
            {
                throw new SecurityException("INVALID_QUESTION_OPTION");
            }

            // if there are performance issues, rethink this strategy (apenas se surgirem problemas de performance)
            foreach (var dbQuestion in dbQuestions)
            {
                bool moreThanOneAnswer = answersViewModel.Count(answer => answer.formQuestionId == dbQuestion.formQuestionId) > 1;

                if (moreThanOneAnswer)
                    throw new InvalidOperationException("INVALID_ANSWERS");

                var dbQuestionOptions = dbQuestion.FormQuestionOptions;

                bool MULTIPLE_RADIO = dbQuestion.type == "MULTIPLE" && !dbQuestion.multipleAnswer;

                if ((dbQuestion.type == "CLASSIFICATION" || dbQuestion.type == "MULTIPLE") && submit)
                {
                    var justifiableOptions = dbQuestionOptions.Where(dbqo => dbqo.requiredJustification).Select(dbqo => dbqo.formQuestionOptionId).ToList();
                    var answeredOptions = answersViewModel.Where(a => a.formQuestionId == dbQuestion.formQuestionId).Select(a => a.FormQuestionResponses).FirstOrDefault()?.Select(r => r.formQuestionOptionId).ToArray();

                    if (answeredOptions != null && answeredOptions.Any(ao => justifiableOptions.Contains(ao)))
                    {
                        var justification = answersViewModel.Where(a => a.formQuestionId == dbQuestion.formQuestionId && a.FormQuestionComment != null)
                        .Select(c => c.FormQuestionComment).FirstOrDefault();

                        if (justification is null || string.IsNullOrWhiteSpace(justification.comment) || justification.comment.Length > 1000 || justification.comment.Length < 5)
                        {
                            throw new InvalidOperationException("INVALID_JUSTIFICATION");
                        }
                    }
                }

                if (dbQuestion.type == "CLASSIFICATION" || MULTIPLE_RADIO)
                {
                    var answer = answersViewModel.Find(a => a.formQuestionId == dbQuestion.formQuestionId);
                    bool moreThanOneOptChosen = answer.FormQuestionResponses.Count > 1;

                    if (moreThanOneOptChosen)
                        throw new InvalidOperationException("INVALID_OPTIONS");
                }

                if (dbQuestion.type == "FREE")
                {
                    var answer = answersViewModel.FirstOrDefault(a => a.formQuestionId == dbQuestion.formQuestionId);
                    List<FormQuestionResponse> free_answer = new List<FormQuestionResponse>() { };

                    if (answer != null && answer.FormQuestionResponses.Any())
                    {
                        var first_valid = answer.FormQuestionResponses.FirstOrDefault(a => !string.IsNullOrWhiteSpace(a.value));

                        if (first_valid != null)
                        {
                            free_answer.Add(first_valid);
                        }
                    }

                    answer.FormQuestionResponses = free_answer;
                }
            }

            if (submit)
            {
                int requiredQuestionsCount = dbQuestions.Count(question => question.required);
                int requiredAnswersCount = answersViewModel.Count(res => res.required);

                if (requiredQuestionsCount != requiredAnswersCount)
                    throw new InvalidOperationException("FORM_NOT_FULLY_ANSWERED");
            }

            var dbQuestionsIdList = dbQuestions.Select(question => question.formQuestionId);
            IEnumerable<FormQuestionAnswerViewModel> filteredAnswers = answersViewModel.Where(a => dbQuestionsIdList.Contains(a.formQuestionId));

            // validating comments
            List<FormQuestionComment> commentsObj = answersViewModel
                .Where(fqc => fqc.FormQuestionComment != null)
                .Select(c => c.FormQuestionComment).ToList();

            if (commentsObj.Any())
            {
                List<int> questionsIdListOfComments = commentsObj.Select(comment => comment.formQuestionId).ToList();
                var answersAssociatedWithComments = filteredAnswers.Where(answer => questionsIdListOfComments.Contains(answer.formQuestionId));

                if (!answersAssociatedWithComments.Any())
                    throw new InvalidOperationException("INVALID_COMMENTS_LIST");

                if (commentsObj.Any(c => c.comment == null))
                {
                    throw new InvalidOperationException("INVALID_COMMENT");
                }
            }


            return filteredAnswers;
        }

        private void ValidateUser()
        {
            // Business validation
            // The user must be active ✓
            if (_user.blocked)
            {
                throw new SecurityException("USER_BLOCKED");
            }
        }

        private void ValidateContent(Content content)
        {
            // Content type check
            if (!content.type.Equals(ContentTypes.Form))
            {
                throw new InvalidOperationException("INVALID_CONTENT_TYPE");
            }

            // Workgroup not archived ✓
            if (content.Workgroup.archived)
            {
                throw new InvalidOperationException("INVALID_WORKGROUP");
            }

            // Client not blocked and/or not deleted ✓
            var client = content.Workgroup.Client;
            if (client.blocked || client.deleted)
            {
                throw new InvalidOperationException("INVALID_CLIENT");
            }
        }

        private async Task<bool> ValidateDuplicateForm
            (Content content,
            int currentUserID,
            WorkgroupViewModel userWorkgroup,
            List<string> Notifications)
        {
            if (!ContentChecker.Check(content))
                Notifications.Add("INVALID_CONTENT");

            if (!content.type.Equals(ContentTypes.Form))
                Notifications.Add("INVALID_CONTENT_TYPE");

            if (string.IsNullOrEmpty(content.title) || content.title.Length > 225)
                Notifications.Add("INVALID_TITLE");

            Form form = content.Forms.FirstOrDefault();

            // Operations based datime expressed as the UTC
            if (form?.expirationDate < DateTime.Now.Date.ToUniversalTime())
                Notifications.Add("EXPIRED_FORM_DEADLINE");

            if (!userWorkgroup.UAC.createForm)
            {
                Notifications.Add("UNAUTHORIZED_ATTEMPT_TO_CREATE_FORM");
                return false;
            }

            var hasPermission = _contentRepository.CheckPermissionsForWorkgroup(content.workgroupId);
            if (!hasPermission || userWorkgroup.archived)
            {
                Notifications.Add("ATTEMPT_TO_INCLUDE_DATA_UNAUTHORIZED");
                return false;
            }

            FeatureManagerService featureService = new FeatureManagerService(currentUserID);
            await featureService.checkPlanQuota(userWorkgroup.clientId, PlanFeatureNames.BOARD_QUOTA, false);

            if (!(await featureService.isEnabledByClient(PlanFeatureNames.FORMS, userWorkgroup.clientId)))
            {
                Notifications.Add("FEATURE_NOT_INCLUDED");
                return false;
            }

            return !Notifications.Any();
        }

        private List<ContentPermission> AddPermissions(IEnumerable<ContentSubscriber> subscribers, int _currentUser)
        {
            var permissions = new List<ContentPermission>();

            permissions.AddRange(subscribers.Select(cs => new ContentPermission()
            {
                createUser = _currentUser,
                userId = cs.userId,
                createDate = DateTime.UtcNow,
                allowed = true,
            }));

            if (!permissions.Any(p => p.userId == _currentUser))
            {
                permissions.Add(new ContentPermission()
                {
                    createUser = _currentUser,
                    userId = _currentUser,
                    createDate = DateTime.UtcNow,
                    allowed = true,
                });
            }

            return permissions;
        }

        private List<FormSection> GenerateNewSections(Form formModelo)
        {
            List<FormSection> newSections = new List<FormSection>();
            foreach (var section in formModelo.FormSections)
            {

                List<FormQuestion> newQuestions = new List<FormQuestion>();
                foreach (var question in section.FormQuestions)
                {

                    List<FormQuestionOption> newOptions = new List<FormQuestionOption>();
                    foreach (var option in question.FormQuestionOptions)
                    {
                        newOptions.Add(new FormQuestionOption()
                        {
                            description = option.description,
                            hidden = option.hidden,
                            optionOrder = option.optionOrder,
                            title = option.title,
                            requiredJustification = option.requiredJustification
                        });
                    }

                    newQuestions.Add(new FormQuestion()
                    {
                        multipleAnswer = question.multipleAnswer,
                        questionOrder = question.questionOrder,
                        deleted = question.deleted,
                        formSectionId = question.formSectionId,
                        required = question.required,
                        showComment = question.showComment,
                        title = question.title,
                        type = question.type,
                        requiredJustification = false, // remember to remove this after backwards compatibility is no longer needed
                        FormQuestionOptions = newOptions
                    });
                }

                newSections.Add(new FormSection()
                {
                    title = section.title,
                    sectionOrder = section.sectionOrder,
                    FormQuestions = newQuestions
                });
            }

            return newSections;
        }

        public async Task<Tuple<Form, List<FormSubscriberDTO>>> GetForm(Guid contentUuid)
        {
            var content = await _contentService.GetSimpleContentAsync(contentUuid);

            var title = content.title;
            var status = content.status;
            var isOwner = (content.createUser == _user.userId) || content.UAC.isOwner;

            this.ValidateContent(content);

            var form = await _repo.GetForm(content.contentUuid, true);

            if (form is null)
            {
                throw new SecurityException("FORM_NOT_FOUND");
            }

            form.totalAnswers = await _repo.GetFormRespondentsTotal(contentUuid);

            var formSubscribers = await _repo.GetFormSubscribers(contentUuid);

            return new Tuple<Form, List<FormSubscriberDTO>>(form, formSubscribers);
        }

        public async Task<object> GetFormToAnswer(int contentId)
        {
            var content = await _contentService.GetSimpleContentAsync(contentId);
            await ValidateGetFormToAnswer(content);

            Form form = await _repo.GetFormToAnswer(content.contentId);

            FormRespondents respondent = await _repo.GetFormRespondent(contentId, _user.userId);
            bool formAnswered = respondent?.status == "CLOSED";

            if (!formAnswered && DateTime.UtcNow > form.expirationDate)
            {
                // There is no need to return form data if the user doesn't answered and
                // form was expired
                throw new InvalidOperationException("EXPIRED_FORM_DEADLINE");
            }

            HandleFormQuestionOptionRequiredJustification(form);

            FormViewModel formViewModel = new FormViewModel();
            formViewModel.answered = formAnswered;
            formViewModel.title = content.title;
            formViewModel.formId = form.formId;
            formViewModel.contentId = content.contentId;
            formViewModel.anonymousAnswer = form.anonymousAnswer;
            formViewModel.FormSections = form.FormSections;

            return formViewModel;
        }

        public void HandleFormQuestionOptionRequiredJustification(Form form)
        {
            foreach (var section in form.FormSections)
            {
                foreach (var question in section.FormQuestions)
                {
                    if ((question.type == "CLASSIFICATION" || question.type == "MULTIPLE") && question.requiredJustification)
                    {
                        foreach (var option in question.FormQuestionOptions)
                        {
                            option.requiredJustification = question.requiredJustification;
                        }
                    }
                }
            }
        }

        public async Task<List<PendingAnswerUsersViewModel>> GetPendingAnswersUsers(int contentId)
        {
            Content content = await _contentService.GetSimpleContentAsync(contentId);

            await ValidateGetFormToCheckPendingAnswers(content);

            return await this.GetFormPendingAnswerUsersList(contentId);
        }

        private async Task<bool> ValidateGetFormToCheckPendingAnswers(Content content)
        {
            ValidateContent(content);

            if (content.status == "OPEN")
            {
                throw new SecurityException("FORM_OPEN");
            }

            if (!content.UAC.isOwner)
            {
                throw new SecurityException("NOT_ALLOWED");
            }

            Form form = await _repo.GetSimpleForm(content.contentId);

            if (form is null)
            {
                throw new SecurityException("FORM_NOT_FOUND");
            }

            if (form.anonymousAnswer)
            {
                throw new InvalidOperationException("ANONYMOUS_FORM");
            }

            return true;
        }

        private void ValidateResendFormRequestAnswer(Content content)
        {
            ValidateContent(content);

            if (content.status != "PUBLISHED")
            {
                throw new InvalidOperationException("FORM_EITHER_CLOSED_OR_OPEN");
            }

            if (!content.UAC.isOwner)
            {
                throw new SecurityException("NOT_ALLOWED");
            }
        }

        private async Task<List<PendingAnswerUsersViewModel>> ValidateResendFormRequestAnswerList(Content content, List<PendingAnswerUsersViewModel> pendingUsers)
        {
            var pendingAnswerUsers = await this.GetFormPendingAnswerUsersList(content.contentUuid);

            if (!pendingAnswerUsers.Any())
            {
                throw new SecurityException("NO_PENDING_USERS");
            }

            var pendingAnswerUsersIds = pendingAnswerUsers.Where(user => !user.hasAnswered).Select(user => user.userId);

            var result = pendingUsers.Where(p => pendingAnswerUsersIds.Contains(p.userId)).ToList();

            if (!result.Any())
            {
                throw new SecurityException("INVALID_PENDING_USERS_LIST");
            }

            return result;
        }

        private async Task<bool> ValidateGetFormToAnswer(Content content)
        {
            ValidateContent(content);
            ValidateUser();

            if (content.status == "OPEN")
                throw new SecurityException("FORM_OPEN");

            if (!await _repo.IsUserFormResponder(content.contentId))
                throw new SecurityException("USER_NOT_A_FORM_RESPONDER");

            return true;
        }

        public async Task<bool> UnpublishSurvey(Guid contentUUid, string userAgent, StorageSettings storageSettings)
        {
            // Roadmap
            // 1. Skip if
            //    the current user isn't the form owner? ✓
            //    the form was already unpublished
            // 2. Unpublish only if the form was published

            var content = await _contentService.GetSimpleContentAsync(contentUUid);
            await ValidateUnpublishSurvey(content);

            var result = await _repo.UnpublishForm(content);
            if (result)
            {
                new ContentActivityService(_user.userId).Add(content.contentId, content.contentUuid, new ContentActivity
                {
                    date = DateTime.UtcNow,
                    type = Operations.FORM_UNPUBLISHED,
                    activityUser = _user.userId,
                    processed = true,
                    contentData = JsonConvert.SerializeObject(new
                    {
                        title = content.title
                    })
                }, userAgent, storageSettings);
            }

            return result;
        }

        private async Task<bool> ValidateUnpublishSurvey(Content content)
        {
            ValidateContent(content);

            if (!content.UAC.can_unpublish_form)
            {
                throw new SecurityException("INVALID_GRANT");
            }

            if (await _contentRepository.FormHasAnyResponses(content))
            {
                throw new SecurityException("FORM_HAS_ANSWERS");
            }

            return true;
        }

        private async Task<int> CheckHowManyDidNotAnswer(int contentId, int userId)
        {
            try
            {
                var formRespondentsList = await _repo.GetAllFormRespondents(contentId);

                var _contentRepo = new ContentRepository(userId);
                var contentSubscribersList = await _contentRepo.GetSubscribersByContentId(contentId);

                return contentSubscribersList.Count - formRespondentsList.Count;
            }
            catch (Exception)
            {
                throw new SecurityException("SOMETHING_WENT_WRONG");
            }
        }

        public async Task<FormConsolidatedReportViewModel> GetConsolidatedFormReport(int contentId)
        {
            var content = await _contentService.GetSimpleContentAsync(contentId);
            this.ValidateGetConsolidatedFormReport(content);

            var FREE_ANSWER = "FREE";

            // get all answers and comments
            var form = await _repo.GetForm(contentId, false);
            var formQuestions = form.FormSections.SelectMany(f => f.FormQuestions);
            var formQuestionIdList = formQuestions.Select(q => q.formQuestionId).ToList();
            var questionOptionsIdList = formQuestions.SelectMany(q => q.FormQuestionOptions).Select(o => o.formQuestionOptionId).ToList();
            var respondents = await _repo.GetAllFormRespondents(contentId);

            if (!respondents.Any())
            {
                throw new InvalidOperationException("NO_ANSWERS_FOUND");
            }

            var respondentIdList = respondents.Select(user => user.userId);
            var answers = await _repo.GetAllAnswersByIdList(questionOptionsIdList, respondentIdList);
            var comments = await _repo.GetAllFormQuestionComments(formQuestionIdList, respondentIdList, !form.anonymousAnswer);

            var formQuestionAnswersCount = await this.GetQuestionAnswersCount(contentId);

            // prepare view model
            var consolidatedReport = new FormConsolidatedReportViewModel();
            var reportQuestions = new List<FormReportQuestion>();
            consolidatedReport.contentId = form.contentId;
            consolidatedReport.title = content.title;
            consolidatedReport.anonymousAnswer = form.anonymousAnswer;
            consolidatedReport.numberOfSections = form.FormSections.Count;

            var sortedSection = form.FormSections.OrderBy(s => s.sectionOrder);
            foreach (var section in sortedSection)
            {
                var sortedQuestion = section.FormQuestions.OrderBy(q => q.questionOrder);
                foreach (var question in sortedQuestion)
                {
                    var reportComments = new List<FormReportQuestionComment>();
                    var allComments = comments.FindAll(cm => cm.formQuestionId == question.formQuestionId).ToList();
                    var reportOptions = new List<FormReportQuestionOption>();

                    foreach (var option in question.FormQuestionOptions)
                    {
                        if (question.type != FREE_ANSWER)
                        {
                            reportOptions.Add(new FormReportQuestionOption()
                            {
                                formQuestionId = question.formQuestionId,
                                formQuestionOptionId = option.formQuestionOptionId,
                                title = option.title,
                                optionOrder = option.optionOrder,
                                requiredJustification = option.requiredJustification,
                                count = answers.Count(a => a.formQuestionOptionId == option.formQuestionOptionId)
                            });

                        }
                        else
                        {
                            var valuesFromFreeAnswer = answers.FindAll(a => a.formQuestionOptionId == option.formQuestionOptionId)
                                                                                        .Select(answer => answer.value)
                                                                                        .ToList();

                            if (valuesFromFreeAnswer.Any())
                                reportOptions.Add(new FormReportQuestionOption()
                                {
                                    formQuestionId = question.formQuestionId,
                                    formQuestionOptionId = option.formQuestionOptionId,
                                    optionOrder = option.optionOrder,
                                    value = valuesFromFreeAnswer
                                });
                        }

                    }

                    foreach (var comment in allComments)
                    {
                        reportComments.Add(new FormReportQuestionComment()
                        {
                            formQuestionId = comment.formQuestionId,
                            formQuestionCommentId = comment.formQuestionCommentId,
                            comment = comment.comment,
                            User = !form.anonymousAnswer ? comment.User : null
                        });
                    }


                    reportQuestions.Add(new FormReportQuestion()
                    {
                        formSectionId = section.formSectionId,
                        sectionTitle = section.title,
                        sectionOrder = section.sectionOrder,
                        formQuestionId = question.formQuestionId,
                        title = question.title,
                        type = question.type,
                        required = question.required,
                        requiredJustification = false, // remember to remove this after backwards compatibility is no longer needed
                        questionOrder = question.questionOrder,
                        FormReportQuestionOptions = reportOptions,
                        FormReportQuestionComments = reportComments,
                        totalAnswers = formQuestionAnswersCount.Find(a => a.formQuestionId == question.formQuestionId)?.answersCount ?? 0
                    });
                }
            }

            consolidatedReport.FormReportQuestion = reportQuestions;
            return consolidatedReport;
        }

        public async Task<List<IndividualReportRespondentViewModel>> GetIndividualFormReport(Content content, List<FormRespondents> pagedformRespondents, bool anonymousAnswer)
        {
            var respondentsUserIdArray = pagedformRespondents.Select(respondent => respondent.userId).ToArray();

            var individualAnswerList = await GetIndividualResponseAnswers(respondentsUserIdArray, content, anonymousAnswer);

            var groupedAnswersByRespondent = individualAnswerList.GroupBy(answer => answer.userId);

            var individualReportRespondentsList = new List<IndividualReportRespondentViewModel>();

            foreach (var respondent in pagedformRespondents)
            {
                var respondentHasIndividualAnswers = individualAnswerList.Any(ra => ra.userId == respondent.userId);

                if (!respondentHasIndividualAnswers)
                {
                    individualReportRespondentsList.Add(new IndividualReportRespondentViewModel
                    {
                        FormRespondentId = respondent.formRespondentId,
                        UserId = respondent.userId,
                        Name = anonymousAnswer ? null : respondent.User.name,
                        ProfilePic = anonymousAnswer ? null : respondent.User.profilePic,
                        UserAnswers = Array.Empty<IndividualReportAnswerViewModel>()
                    });
                }
            }

            foreach (var answer in groupedAnswersByRespondent)
            {
                var firstAnswer = answer.First();

                individualReportRespondentsList.Add(new IndividualReportRespondentViewModel
                {
                    FormRespondentId = firstAnswer.formRespondentId,
                    UserId = answer.Key,
                    Name = firstAnswer.name,
                    ProfilePic = firstAnswer.profilePic,
                    UserAnswers = answer.ToArray()
                });
            }

            if (!anonymousAnswer)
            {
                return individualReportRespondentsList.OrderBy(respondent => respondent.Name).ToList();
            }

            return individualReportRespondentsList.OrderBy(respondent => respondent.UserId).ToList();
        }

        public async Task<PagedListIndividualReportViewModel> GetPagedIndividualFormReport(int contentId, int page = 1)
        {
            page = page < 1 ? 1 : page;

            var content = await _contentService.GetSimpleContentAsync(contentId);
            this.ValidateGetIndividualFormReport(content);

            var form = await _repo.GetForm(contentId, true);

            var skip = (page - 1) * _filter.pageSize.Value;

            var pagedFormRespondents = await _repo.GetAllFormRespondentsForPaging(contentId, skip);

            if (!pagedFormRespondents.Any())
            {
                return new PagedListIndividualReportViewModel
                {
                    PageNumber = page,
                    Form = null,
                    IndividualResponses = null,
                    NumberOfUsersPerPage = _filter.pageSize.Value,
                    TotalNumberOfRespondents = 0
                };
            }

            var totalRespondents = await _repo.GetFormRespondentsTotal(contentId);

            var individualResults = await this.GetIndividualFormReport(content, pagedFormRespondents, form.anonymousAnswer);

            return new PagedListIndividualReportViewModel()
            {
                PageNumber = page,
                Form = form,
                IndividualResponses = individualResults,
                NumberOfUsersPerPage = _filter.pageSize.Value,
                TotalNumberOfRespondents = totalRespondents,
            };
        }

        public async Task<List<QuestionAnswersCountViewModel>> GetQuestionAnswersCount(int contentId)
        {
            string query = $@"
                SELECT
                    T.formQuestionId, COUNT(*) answersCount
                    FROM (
                        SELECT
                        DISTINCT
                        R.[userId],
                        Q.formQuestionId
                        FROM [Content] C
                        JOIN [Form] F ON C.contentId = F.contentId
                        JOIN [FormSection] S ON F.formId = S.formId
                        JOIN [FormQuestion] Q ON S.formSectionId = Q.formSectionId
                        JOIN [FormQuestionOption] O ON O.formQuestionId = Q.formQuestionId
                        JOIN [FormQuestionResponse] R ON R.formQuestionOptionId = O.formQuestionOptionId
                        JOIN [FormRespondents] FR ON FR.contentId = C.contentId AND FR.userId = R.userId AND FR.[status] = 'CLOSED'
                        WHERE C.contentId = @contentId AND C.[type] = 'Form' AND C.deleted <> 1
                    ) AS T
                GROUP BY T.formQuestionId
                ORDER BY T.formQuestionId";

            using (var model = new AtlasModelCore())
            {
                var answersCountList = await model.Database
                    .SqlQueryRaw<QuestionAnswersCountViewModel>(query, new SqlParameter("@contentId", contentId))
                    .ToListAsync();

                return answersCountList;
            }
        }

        public async Task<DetailedReportViewModel> GetDetailedFormReport(int contentId)
        {
            var content = await _contentService.GetSimpleContentAsync(contentId);
            this.ValidateGetDetailedFormReport(content);

            var form = await _repo.GetSimpleForm(contentId);
            var questions = await _repo.GetFormQuestions(content);
            var respondents = await _repo.GetFormRespondents(contentId);

            var detailedAnswerDict = await this.GetDetailedReportAnswers(contentId);

            List<DetailReportAnswerViewModel> questionAnswers;
            List<DetailedReportQuestionAnswerViewModel> answersTable = new List<DetailedReportQuestionAnswerViewModel>();
            foreach (var question in questions)
            {
                questionAnswers = new List<DetailReportAnswerViewModel>();
                foreach (var user in respondents)
                {
                    string key = $"{user.userId}:{question.formQuestionId}";

                    detailedAnswerDict.TryGetValue(key, out string answer);
                    questionAnswers.Add(new DetailReportAnswerViewModel()
                    {
                        userId = user.userId,
                        formQuestionId = question.formQuestionId,
                        answer = answer
                    });
                }

                answersTable.Add(new DetailedReportQuestionAnswerViewModel()
                {
                    Title = question.title,
                    Type = question.type,
                    FormQuestionId = question.formQuestionId,
                    FormSectionId = question.formSectionId,
                    QuestionOrder = question.questionOrder,
                    Answers = questionAnswers
                });
            }

            bool anonymousAnswer = form?.anonymousAnswer ?? false;
            var result = new DetailedReportViewModel()
            {
                AnonymousAnswer = anonymousAnswer,
                Questions = questions,
                Respondents = respondents.Select(u => new DetailedReportRespondentViewModel()
                {
                    UserId = u.userId,
                    Name = anonymousAnswer ? null : u.name,
                    ProfilePic = anonymousAnswer ? null : u.profilePic
                }),
                Answers = answersTable
            };

            return result;
        }

        public async Task<DetailedReportViewModel> GetDetailedFormReportToExport(int contentId)
        {
            var content = await _contentService.GetSimpleContentAsync(contentId);
            this.ValidateGetDetailedFormReport(content);

            var questions = await _repo.GetFormQuestions(content);

            var form = await _repo.GetSimpleForm(contentId);
            var detailedAnswerList = await this.GetDetailedReportAnswers(content);

            var answersGrouped = detailedAnswerList.GroupBy(answer => answer.userId);

            var detailedReportRespondents = new List<DetailedReportRespondentViewModel>();

            foreach (var group in answersGrouped)
            {
                var userQuestionResponseDict = group.ToDictionary(a => a.formQuestionId, a => a);

                var fullDetailReportAnswers = questions.Select(question =>
                {
                    DetailReportAnswerViewModel detailedAnswerFound = null;
                    userQuestionResponseDict.TryGetValue(question.formQuestionId, out detailedAnswerFound);
                    return new DetailReportAnswerViewModel()
                    {
                        userId = group.Key,
                        answer = detailedAnswerFound?.answer,
                        formQuestionId = question.formQuestionId,
                        type = question.type,
                        title = question.title,
                        formSectionId = question.formSectionId,
                        questionOrder = question.questionOrder
                    };
                });

                var firstAnswer = group.First();
                detailedReportRespondents.Add(new DetailedReportRespondentViewModel()
                {
                    UserId = group.Key,
                    Name = firstAnswer.name,
                    ProfilePic = firstAnswer.profilePic,
                    Answers = fullDetailReportAnswers
                });
            }

            var respondentsNoAnswer = await GetRespondentsWithoutAnyAnswer(content);
            detailedReportRespondents.AddRange(respondentsNoAnswer);

            var sortedDetailedReportRespondents = detailedReportRespondents.OrderBy(r => r.Name).ToArray();

            bool anonymousAnswer = form?.anonymousAnswer ?? false;
            var result = new DetailedReportViewModel()
            {
                AnonymousAnswer = anonymousAnswer,
                Questions = questions,
                Respondents = sortedDetailedReportRespondents
            };

            return result;
        }

        private async Task<IList<DetailedReportRespondentViewModel>> GetRespondentsWithoutAnyAnswer(Content content)
        {
            string query = $@"
                SELECT
                    FR2.UserId,
                    U.[Name],
                    U.ProfilePic
                FROM [FormRespondents] FR2
                JOIN [Content] C ON C.contentId = FR2.contentId
                JOIN [User] U ON U.userId = FR2.userId
                WHERE
                    C.[type] = 'Form' AND C.deleted <> 1 AND
                    FR2.contentId = @contentId AND FR2.[status] = 'CLOSED' AND
                    NOT EXISTS(
                        SELECT 1
                        FROM [Form] F
                        JOIN [FormSection] S ON S.formId = F.formId
                        JOIN [FormQuestion] Q ON S.formSectionId = Q.formSectionId
                        JOIN [FormQuestionOption] O ON O.formQuestionId = Q.formQuestionId
                        JOIN [FormQuestionResponse] R ON R.formQuestionOptionId = O.formQuestionOptionId
                        JOIN [Content] C ON C.contentId = F.contentId
                        WHERE C.contentId = FR2.contentId AND R.userId = FR2.userId
                   )
                ORDER BY U.[Name]";

            AtlasModelCore model = new AtlasModelCore();
            var respondents = await model.Set<DetailedReportRespondentViewModel>()
                .FromSqlRaw(query, new SqlParameter("@contentId", content.contentId))
                .ToListAsync();

            return respondents;
        }

        private void ValidateGetConsolidatedFormReport(Content content)
        {
            ValidateContent(content);

            if (!content.UAC.can_view_report)
            {
                throw new SecurityException("INVALID_GRANT_CONSOLIDATED");
            }
        }

        private void ValidateGetIndividualFormReport(Content content)
        {
            ValidateContent(content);

            if (!content.UAC.can_view_report)
            {
                throw new SecurityException("INVALID_GRANT_INDIVIDUAL");
            }
        }

        private void ValidateGetDetailedFormReport(Content content)
        {
            ValidateContent(content);

            if (!content.UAC.can_view_report)
            {
                throw new SecurityException("INVALID_GRANT_DETAILED");
            }
        }

        private async Task<Dictionary<string, string>> GetDetailedReportAnswers(int contentId)
        {
            string query = $@"
                SELECT
                       R.[userId],
                       STRING_AGG(ISNULL(R.value,O.title),'; ') answer,
                       Q.formQuestionId,
                       Q.type,
                       Q.title,
                       S.formSectionId,
                       S.sectionOrder,
                       U.[name],
                       U.profilePic,
                       Q.questionOrder
                FROM [FormSection] S
                JOIN [FormQuestion] Q ON S.formSectionId = Q.formSectionId
                JOIN [FormQuestionOption] O ON O.formQuestionId = Q.formQuestionId
                JOIN [FormQuestionResponse] R ON R.formQuestionOptionId = O.formQuestionOptionId
                JOIN [Form] F ON F.formId = S.formId
                JOIN [Content] C ON C.contentId = F.contentId
                JOIN [FormRespondents] FR ON FR.contentId = C.contentId AND FR.userId = R.userId AND FR.[status] = 'CLOSED'
                JOIN [User] U ON U.userId = FR.userId
                WHERE C.contentId = @contentId AND C.[type] = 'Form' AND C.deleted <> 1
                GROUP BY R.userId, Q.formQuestionId, Q.[type], Q.title,
                         S.formSectionId, S.sectionOrder,
                         U.[name], U.profilePic,
                         Q.questionOrder
                ORDER BY sectionOrder,questionOrder,U.[name]";

            AtlasModelCore model = new AtlasModelCore();
            var detailedAnswerDict = await model.Set<DetailReportAnswerViewModel>()
                .FromSqlRaw(query, new SqlParameter("@contentId", contentId))
                .ToDictionaryAsync(a => $"{a.userId}:{a.formQuestionId}", a => a.answer);

            return detailedAnswerDict;
        }

        private async Task<IList<DetailReportAnswerViewModel>> GetDetailedReportAnswers(Content content)
        {
            string query = $@"
                SELECT
                    R.[userId],
                    STRING_AGG(ISNULL(R.value,O.title),'; ') answer,
                    Q.formQuestionId,
                    Q.type,
                    Q.title,
                    S.formSectionId,
                    S.sectionOrder,
                    U.[name],
                    U.profilePic,
                    Q.questionOrder
                FROM [FormSection] S
                JOIN [FormQuestion] Q ON S.formSectionId = Q.formSectionId
                JOIN [FormQuestionOption] O ON O.formQuestionId = Q.formQuestionId
                JOIN [FormQuestionResponse] R ON R.formQuestionOptionId = O.formQuestionOptionId
                JOIN [Form] F ON F.formId = S.formId
                JOIN [Content] C ON C.contentId = F.contentId
                JOIN [FormRespondents] FR ON FR.contentId = C.contentId AND FR.userId = R.userId AND FR.[status] = 'CLOSED'
                JOIN [User] U ON U.userId = FR.userId
                WHERE C.contentId = @contentId AND C.[type] = 'Form' AND C.deleted <> 1
                GROUP BY R.userId, Q.formQuestionId, Q.[type], Q.title,
                         S.formSectionId, S.sectionOrder,
                         U.[name], U.profilePic,
                         Q.questionOrder
                ORDER BY userId,sectionOrder, questionOrder";

            AtlasModelCore model = new AtlasModelCore();
            var detailedAnswerList = await model.Set<DetailReportAnswerViewModel>()
                .FromSqlRaw(query, new SqlParameter("@contentId", content.contentId))
                .ToListAsync();

            return detailedAnswerList;
        }

        private async Task<IList<IndividualReportAnswerViewModel>> GetIndividualResponseAnswers(int[] userIds, Content content, bool anonymousAnswer)
        {
            string additionalColumns = !anonymousAnswer ? "U.[name], U.profilePic," : "";

            string query = $@"SELECT
                            R.[userId],
                            ISNULL(R.value,O.title) answer,
                            O.formQuestionOptionId,
	                        FQC.comment,
                            Q.formQuestionId,
                            Q.type,
                            Q.title,
                            Q.multipleAnswer,
                            {additionalColumns}
                            S.formSectionId,
                            FR.formRespondentId
                            FROM [FormSection] S
                            JOIN [FormQuestion] Q ON S.formSectionId = Q.formSectionId
                            JOIN [FormQuestionOption] O ON O.formQuestionId = Q.formQuestionId
                            JOIN [FormQuestionResponse] R ON R.formQuestionOptionId = O.formQuestionOptionId
                            JOIN [Form] F ON F.formId = S.formId
                            JOIN [Content] C ON C.contentId = F.contentId
                            JOIN [FormRespondents] FR ON FR.contentId = C.contentId AND FR.userId = R.userId AND FR.[status] = 'CLOSED'
                            JOIN [User] U ON U.userId = FR.userId
                            LEFT JOIN [FormQuestionComment] FQC ON Q.formQuestionId = FQC.formQuestionId AND FQC.userId = R.userId
                            WHERE C.contentId = @contentId AND C.[type] = 'Form' AND C.deleted <> 1 AND FR.userId IN ({String.Join(",", userIds)})
                            ORDER BY sectionOrder, questionOrder";

            var model = new AtlasModelCore();

            var individualAnswerList = await model.Set<IndividualReportAnswerViewModel>()
                .FromSqlRaw(query,
                new SqlParameter("@contentId", content.contentId))
                .ToListAsync();

            if (individualAnswerList != null)
            {
                //not sure if this is needed, but given the complexity of the query above, let's just stick with it for now... Certainly not ideal!
                string queryRequiredJustifications = $@"SELECT DISTINCT 
                                            FQC.userId,
                                            NULL AS answer,
                                            0 AS formQuestionOptionId,
                                            FQC.comment,
                                            Q.formQuestionId,
                                            Q.type,
                                            Q.title,
                                            Q.multipleAnswer,
                                            {additionalColumns}
                                            S.formSectionId,
                                            FR.formRespondentId
                                        FROM [FormQuestionComment] FQC
                                        JOIN [FormQuestion] Q ON Q.formQuestionId = FQC.formQuestionId
                                        JOIN [FormQuestionOption] QO ON Q.formQuestionId = QO.formQuestionId
                                        JOIN [FormSection] S ON S.formSectionId = Q.formSectionId
                                        JOIN [Form] F ON F.formId = S.formId
                                        JOIN [Content] C ON C.contentId = F.contentId
                                        JOIN [FormRespondents] FR ON FR.contentId = C.contentId AND FR.userId = FQC.userId AND FR.[status] = 'CLOSED'
                                        JOIN [User] U ON U.userId = FR.userId
                                        WHERE C.contentId = @contentId 
                                          AND C.[type] = 'Form' 
                                          AND C.deleted <> 1 
                                          AND FQC.userId IN ({String.Join(",", userIds)})
                                          AND QO.requiredJustification = 1
                                          AND NOT EXISTS (
                                            SELECT 1
                                            FROM [FormQuestionResponse] R
                                            WHERE R.formQuestionOptionId = QO.formQuestionOptionId 
                                              AND R.userId = FQC.userId )";

                var justificationsList = await model.Set<IndividualReportAnswerViewModel>().FromSqlRaw(queryRequiredJustifications, new SqlParameter("@contentId", content.contentId)).ToListAsync();

                individualAnswerList.AddRange(justificationsList);
            }

            return individualAnswerList;
        }

        [Obsolete("Must use the GetFormPendingAnswerUsersList(Guid contentUuid) version instead.")]
        private async Task<List<PendingAnswerUsersViewModel>> GetFormPendingAnswerUsersList(int contentId)
        {
            string query = $@"SELECT C.contentId AS contentId, 
                                CS.userId AS userId, 
                                CAST(CASE
                                        WHEN FR.status = 'CLOSED' THEN 1
                                        ELSE 0
                                        END AS BIT) AS hasAnswered,
                                U.email AS userEmail, 
                                U.name AS userName, 
                                ISNULL(U.profilePic, '') AS profilePic
                                FROM Content AS C
                                LEFT JOIN ContentSubscriber AS CS ON CS.contentId = C.contentId
                                LEFT JOIN FormRespondents AS FR ON FR.userId = CS.userId AND FR.contentId = CS.contentId
                                LEFT JOIN [User] AS U ON U.userId = CS.userId
                                WHERE C.type = 'FORM' AND C.deleted <> 1 AND C.contentId = @contentId AND U.blocked <> 1
                                ORDER BY U.name";

            var model = new AtlasModelCore();

            var pendingAnswersUsersList = await model.Set<PendingAnswerUsersViewModel>()
                .FromSqlRaw(query,
                new SqlParameter("@contentId", contentId))
                .ToListAsync();

            return pendingAnswersUsersList;
        }
        private async Task<List<PendingAnswerUsersViewModel>> GetFormPendingAnswerUsersList(Guid contentUuid)
        {
            string query = $@"SELECT 
                                C.contentId AS contentId, 
                                C.contentUuid AS contentUuid,
                                CS.userId AS userId, 
                                CAST(CASE
                                        WHEN FR.status = 'CLOSED' THEN 1
                                        ELSE 0
                                        END AS BIT) AS hasAnswered,
                                U.email AS userEmail, 
                                U.name AS userName, 
                                ISNULL(U.profilePic, '') AS profilePic
                                FROM Content AS C
                                LEFT JOIN ContentSubscriber AS CS ON CS.contentId = C.contentId
                                LEFT JOIN FormRespondents AS FR ON FR.userId = CS.userId AND FR.contentId = CS.contentId
                                LEFT JOIN [User] AS U ON U.userId = CS.userId
                                WHERE C.type = 'FORM' AND C.deleted <> 1 AND C.contentUuid = @contentUuid AND U.blocked <> 1
                                ORDER BY U.name";

            var model = new AtlasModelCore();
            
            var pendingAnswersUsersList   = await model.Database.SqlQueryRaw<PendingAnswerUsersViewModel>(query, new SqlParameter("@contentUuid", contentUuid)).ToListAsync();

            return pendingAnswersUsersList;
        }

        public int RunFormExpirationVerifier()
        {
            // Roadmap
            // 1. Get the list of overdue PUBLISHED forms
            // 2. Iterate the forms to
            //    2.1 Change the content.status to CLOSED
            //    2.2 Update the Content.closeDate property
            //    2.3 Add the content activity
            // 3. Return the result of the verifier

            Content[] contentList;
            int entries = 0;
            using (var model = new AtlasModelCore())
            {
                contentList = (
                        from c in model.Content
                        join f in model.Forms on c.contentId equals f.contentId
                        where c.type == ContentTypes.Form && c.deleted != true &&
                            c.status == "PUBLISHED" && f.expirationDate < DateTime.UtcNow
                        orderby f.publishDate
                        select c
                    )
                    .ToArray();

                DateTime now = DateTime.UtcNow;
                foreach (var content in contentList)
                {
                    content.status = "CLOSED";
                    content.lastUpdate = now;
                    content.closeDate = now;
                }

                entries = model.SaveChanges();
            }

            foreach (var content in contentList)
            {
                var contentActivityService = new ContentActivityService(content.createUser);
                contentActivityService.Add(content.contentId, new ContentActivity()
                {
                    date = DateTime.UtcNow,
                    type = Operations.FORM_EXPIRED,
                    activityUser = content.createUser,
                    processed = true,
                    contentData = JsonConvert.SerializeObject(new { content.title, content.createUser }),
                });
            }

            return entries;
        }

        public async Task<byte[]> ExportDetailedFormReport(int contentId)
        {
            var detailedReportViewModel = await this.GetDetailedFormReportToExport(contentId);

            var questionCommentsDict = await _repo.GetFormQuestionComments(contentId);

            var service = new ExportService(_user);
            var workbook = service.ExportDetailedFormReportToExcel(detailedReportViewModel, questionCommentsDict);

            new ContentActivityService(_user.userId).Add(contentId, new ContentActivity
            {
                date = DateTime.UtcNow,
                type = Operations.FORM_REPORT_EXPORTED,
                activityUser = _user.userId,
                processed = true
            });

            return workbook;
        }

        public async Task<byte[]> ExportConsolidatedFormReport(int contentId)
        {
            var consolidatedReportViewModel = await GetConsolidatedFormReport(contentId);

            var exportService = new ExportService(_user);
            var workbook = exportService.ExportConsolidatedFormReportToExcel(consolidatedReportViewModel);

            new ContentActivityService(_user.userId).Add(contentId, new ContentActivity
            {
                date = DateTime.UtcNow,
                type = Operations.FORM_REPORT_EXPORTED,
                activityUser = _user.userId,
                processed = true
            });

            return workbook;
        }

        public async Task<bool> SendMailToPendingAnswersUsers
        (
            Guid contentUuid, 
            List<PendingAnswerUsersViewModel> pendingUsers,
            string userAgent,
            StorageSettings storageSettings
        )
        {
            var content = await _contentService.GetSimpleContentAsync(contentUuid);

            ValidateResendFormRequestAnswer(content);

            var form = await _repo.GetSimpleForm(content.contentUuid);

            if (form is null)
            {
                throw new SecurityException("FORM_NOT_FOUND");
            }

            if (form.anonymousAnswer)
            {
                var anonymousPendingUsers = await this.GetFormPendingAnswerUsersList(contentUuid);

                if (!anonymousPendingUsers.Any())
                {
                    throw new InvalidOperationException("NO_PENDING_USERS");
                }

                new ContentActivityService(_user.userId).Add(content.contentId, content.contentUuid, new ContentActivity
                {
                    date = DateTime.UtcNow,
                    type = Operations.FORM_REQUEST_ANSWER_RESEND,
                    activityUser = _user.userId,
                    contentData = JsonConvert.SerializeObject(new
                    {
                        title = content.title,
                        contentId = content.contentId,
                        pendingAnswers = anonymousPendingUsers.Where(u => !u.hasAnswered).Select(u => u.userId).ToArray()
                    })
                }, userAgent, storageSettings);

                return true;
            }

            if (!pendingUsers.Any())
            {
                throw new InvalidOperationException("NO_PENDING_USERS");
            }

            pendingUsers = await ValidateResendFormRequestAnswerList(content, pendingUsers);

            new ContentActivityService(_user.userId).Add(content.contentId, content.contentUuid, new ContentActivity
            {
                date = DateTime.UtcNow,
                type = Operations.FORM_REQUEST_ANSWER_RESEND,
                activityUser = _user.userId,
                contentData = JsonConvert.SerializeObject(new
                {
                    title = content.title,
                    contentId = content.contentId,
                    pendingAnswers = pendingUsers.Select(u => u.userId).ToArray()
                })
            }, userAgent, storageSettings);

            return true;
        }

        public async Task<FormCreateResponseDto> CreateAsync(int workgroupId, Content obj, string userAgent, bool supressActivity = false)
        {
            obj.type = ContentTypes.Form;
            List<ContentOwner> contentOwner = new List<ContentOwner>();
            List<ContentActivity> activities = new List<ContentActivity>();

            if (activities == null)
            {
                activities = new List<ContentActivity>();
            }

            // Valida tamanho do title do content
            if ((obj.title != null && obj.title.Length > 10000)
                || (obj.type == ContentTypes.Announcement && string.IsNullOrWhiteSpace(obj.title)))
            {
                throw new ArgumentNullException("ERROR_TITLE");
            }

            // Check Workgroup deletion
            // WI: 618
            WorkgroupService ws = new WorkgroupService(_user.userId);
            var ws1 = await ws.Get(workgroupId, includeHomeData: false);
            if (ws1.archived)
            {
                throw new Exception("BOARD ARCHIVED");
            }

            WorkgroupRepository _w_repo = new WorkgroupRepository(_user.userId);
            var lista_users_workgroup = _w_repo.GetAllUsersFromWorkgroup(workgroupId);
            var boardOwners = ws1.WorkgroupOwner;

            if (!ws1.UAC.createForm)
            {
                throw new SecurityException("Unauthorized attempt to create form");
            }
            if (string.IsNullOrEmpty(obj.title) || obj.title.Length > 225)
            {
                throw new InvalidOperationException("INVALID_TITLE");
            }

            var sanitizer = new HtmlSanitizer();
            sanitizer.AllowedTags.Clear();
            obj.title = Regex.Replace(sanitizer.Sanitize(obj.title), @"[^\w\s]", "");

            var form = obj.Forms.First();

            // Operations based datime expressed as the UTC
            if (form.expirationDate < DateTime.Now.Date.ToUniversalTime())
            {
                // Form expiration is a past date
                throw new InvalidOperationException("EXPIRED_FORM_DEADLINE");
            }

            var permissions = new List<ContentPermission>();

            // All form responders along with the user creator should have permission.
            permissions.AddRange(obj.ContentSubscriber.Select(cs => new ContentPermission()
            {
                createUser = _user.userId,
                userId = cs.userId,
                createDate = DateTime.UtcNow,
                allowed = true,
            }));

            if (!permissions.Any(p => p.userId == _user.userId))
            {
                permissions.Add(new ContentPermission()
                {
                    createUser = _user.userId,
                    userId = _user.userId,
                    createDate = DateTime.UtcNow,
                    allowed = true,
                });
            }

            contentOwner.Add(new ContentOwner()
            {
                userId = _user.userId,
                createUser = _user.userId,
                createDate = DateTime.UtcNow
            });

            //default subscribers
            var subscribers = new List<ContentSubscriber>();

            subscribers.AddRange(obj.ContentSubscriber.Where(o => lista_users_workgroup.Select(a => a.userId).Contains(o.userId)));

            if (!subscribers.Any())
            {
                throw new SecurityException("NO_FORM_RESPONDERS");
            }

            //check if types contains the respective object arrays (ex. Content object type=Task, should have the Task object array filled)
            if (Helpers.ContentChecker.Check(obj))
            {
                Content res_repo = null;

                var titleSanitized = obj.Meeting.FirstOrDefault()?.title ?? obj.MeetingAgendaItem.FirstOrDefault()?.title ?? obj.title;
                if (!string.IsNullOrEmpty(titleSanitized))
                {
                    titleSanitized = Regex.Replace(titleSanitized, @"[<>]", "");

                    obj.title = titleSanitized;
                }

                try
                {
                    res_repo = _contentRepository.Add(workgroupId, obj, permissions, subscribers, contentOwner, activities);
                }
                catch (Exception ex)
                {
                    var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                    var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage("Error while creating content"));
                    sentryEvent.Extra = new
                    {
                        ex,
                        obj,
                        permissions,
                        subscribers,
                        contentOwner,
                        activities
                    };
                    ravenClient.Capture(sentryEvent);

                    return new FormCreateResponseDto { Id = Guid.Empty };
                }

                //if success Add()
                if (res_repo != null)
                {
                    //todo: temporary fix to avoid ContentActivity for Minutes of type Attachment
                    if (supressActivity)
                    {
                        return new FormCreateResponseDto { Id = res_repo.contentUuid };
                    }

                    var title = "";

                    title = obj.title;

                    //todo: mover para business
                    activities = new List<ContentActivity>();

                    if (obj.type != ContentTypes.Note)
                    {
                        //S-32 - For announcements of type 'systemGenerated', use the createDate of the previous contentActivity that triggered the autoGen
                        var createDate = DateTime.UtcNow;

                        string contentData = JsonConvert.SerializeObject(new
                        {
                            title = title
                        });

                        bool processed = false;

                        if (obj.type == ContentTypes.Poll)
                        {
                            var poll = obj.Poll.First();
                            processed = poll.hidden ?? false;

                            contentData = JsonConvert.SerializeObject(new
                            {
                                title,
                                poll.hidden // save useful evidences for future analysis
                            }, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        }

                        string subItemType = "";

                        if (obj.type == ContentTypes.MeetingAgendaItem)
                        {
                            var agendaItem = obj.MeetingAgendaItem.FirstOrDefault();

                            if (agendaItem != null && agendaItem.agendaItemType == "GUEST" && agendaItem.guestNotification == true)
                            {
                                if (string.IsNullOrEmpty(agendaItem.additionalMessageExtAssigner))
                                {
                                    contentData = JsonConvert.SerializeObject(new
                                    {
                                        title,
                                        sendAgendaTitleToExternalAssigner = agendaItem.sendAgendaTitleExtAssigner
                                    });
                                }
                                else
                                {
                                    contentData = JsonConvert.SerializeObject(new
                                    {
                                        title,
                                        sendAgendaTitleToExternalAssigner = agendaItem.sendAgendaTitleExtAssigner,
                                        additionalMessage = true,
                                    });
                                }

                                subItemType = SubItems.AGENDA_TITLE_EXT_ASSIGNER;
                            }
                        }

                        new ContentActivityService(_user.userId).Add(res_repo.contentId, res_repo.contentUuid, new ContentActivity()
                        {
                            date = createDate,
                            type = "CREATED",
                            activityUser = _user.userId,
                            contentData = contentData,
                            subItemType = subItemType,
                            processed = processed,
                        }, userAgent);
                    }
                }

                return new FormCreateResponseDto { Id = res_repo.contentUuid };
            }
            else
            {
                return new FormCreateResponseDto { Id = Guid.Empty };
            }
        }

        public async Task<bool> RepublishForm(int contentId)
        {
            var contentForm = await _contentService.Get(contentId) ?? throw new InvalidOperationException("INVALID_GRANT");

            if (!contentForm.UAC.canReopenForm || !contentForm.Forms.Any())
                throw new InvalidOperationException("INVALID_GRANT");

            var repusblished = await _repo.RepublishForm(contentForm);

            if (repusblished)
            {
                new ContentActivityService(_user.userId).Add(contentId, new ContentActivity()
                {
                    date = DateTime.UtcNow,
                    type = Operations.FORM_REPUBLISHED,
                    activityUser = _user.userId,
                    processed = true,
                    contentData = JsonConvert.SerializeObject(new
                    {
                        title = contentForm.title
                    })
                });
            }

            return repusblished;
        }
    }
}
