using AngleSharp.Html.Parser;
using Atlas.Business.Helpers;
using Atlas.Business.ViewModels;
using Atlas.Business.ViewModels.Announcemets;
using Atlas.Business.ViewModels.Attachments;
using Atlas.Business.ViewModels.Subscriber;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Ganss.Xss;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SharpRaven;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security;
using System.Security.Principal;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using TimeZoneConverter;

namespace Atlas.Business.Core.Announcement
{
    public class AnnouncementService : IAnnouncementService
    {
        private readonly AuthUtil _authUtil;
        private readonly ContentRepository _repo;
        private readonly int _currentUser;

        public AnnouncementService
        (
            IPrincipal principal
        )
        {
            _authUtil = new AuthUtil(principal);
            _repo = new ContentRepository(_authUtil.UserId);
            _currentUser = _authUtil.UserId;
        }

        public async Task<AnnouncementsResponseViewModel> GetAnnouncements(AnnouncementRequestFilter filters)
        {
            string dbTimezone = string.IsNullOrWhiteSpace(_repo._currentUser.defaultTimezone)
                                ? "America/Sao_Paulo"
                                : _repo._currentUser.defaultTimezone;
            string timezoneId = "";

            try
            {
                timezoneId = TZConvert.IanaToWindows(dbTimezone);
            }
            catch (InvalidTimeZoneException)
            {
                timezoneId = "E. South America Standard Time";
            }

            TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);

            List<AnnouncementViewModel> result = await GetFilteredAnnouncements(filters);

            if (!string.IsNullOrEmpty(filters.searchTerm))
            {
                result = result.Where(o => o.title.Contains(filters.searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
            }


            var response = new AnnouncementsResponseViewModel();

            if (!string.IsNullOrEmpty(filters.groupBy))
            {
                response.isGrouped = true;
                if (filters.groupBy == "createDate")
                {
                    // Group by date (ignoring time)
                    Func<DateTime, DateTime> truncateTime = (date) =>
                    {
                        var dateUtc = TimeZoneInfo.ConvertTimeFromUtc(date, userTimeZone);
                        return new DateTime(dateUtc.Year, dateUtc.Month, dateUtc.Day);
                    };

                    response.Groups = result
                        .GroupBy(o => truncateTime(o.createDate))
                        .Select(g => new AnnouncementGroupViewModel
                        {
                            key = g.Key,
                            keyName = filters.groupBy,
                            keyType = "DateTime",
                            Items = g.ToList(),
                            count = g.Count() // Total items in this group
                        }).ToList();
                }
                else
                {
                    System.Reflection.PropertyInfo prop = typeof(AnnouncementViewModel).GetProperty(filters.groupBy);

                    response.Groups = result
                        .GroupBy(x => prop.GetValue(x, null))
                        .Select(g => new AnnouncementGroupViewModel
                        {
                            key = g.Key,
                            keyName = filters.groupBy,
                            keyType = prop.PropertyType.Name,
                            Items = g.ToList(),
                            count = g.Count() // Total items in this group
                        }).ToList();
                }
            }
            else
            {
                response.isGrouped = false;
                response.Announcements = result;
            }

            return response;
        }
        public async Task<AnnouncementDetailsViewModel> GetAnnouncementDetails(Guid contentId)
        {
            #region current user can see the annoucement?
            string sqlStatement = @"
                                    SELECT COUNT(*) as Value
                                    FROM [Content] c 
                                    INNER JOIN [Workgroup] w ON c.workgroupId = w.workgroupId 
                                    INNER JOIN [WorkgroupUser] wu on w.workgroupId = wu.workgroupId 
                                    INNER JOIN [Client] cl ON cl.clientId = w.clientId  
                                    WHERE wu.userId = @userId 
                                    AND c.contentUuid = @contentUuid 
                                    AND cl.deleted = 0 AND cl.blocked = 0";

            var _md = new AtlasModelCore();
            var count = _md.Database.SqlQueryRaw<int>(sqlStatement, new SqlParameter("@userId", _authUtil.UserId),
                                                                    new SqlParameter("@contentUuid", contentId))
                                    .FirstOrDefault();

            if (count == 0)
            {
                throw new SecurityException("Unauthorized attempt to access announcement details.");
            }
            #endregion

            var result = new AnnouncementDetailsViewModel();

            #region announcement details
            sqlStatement = @"
                                    SELECT 
                                        c.contentUuid,
	                                    c.title,
                                        c.createDate,
                                        c.workgroupId,

                                        a.originalContentUuid,
	                                    a.announcementId,
	                                    a.body,
	                                    a.type as announcementType,
	                                    a.originalContentType,
	                                    
                                        u.userId as authorId,
                                        u.name as authorName,
                                        u.email as authorEmail,
	                                    u.profilePic,
                                        u.invitationPending,
                                        
                                        w.name as workgroupName,
                                        w.bulletColor,
                                        w.type as workgroupType,
                                        
                                        cl.clientId,
                                        cl.name as clientName
                                       
                                    FROM [Announcement] a 
                                    INNER JOIN [Content] c 
                                    ON (a.contentUuid = c.contentUuid)
                                    INNER JOIN [User] u 
                                    ON (c.createUser = u.userId) 
                                    INNER JOIN [Workgroup] w 
                                    ON (c.workgroupId = w.workgroupId) 
                                    INNER JOIN [Client] cl 
                                    ON (w.clientId = cl.clientId) 
                                    WHERE c.contentUuid = @contentId 
                                    AND c.deleted <> 1";

            var announcement = _md.Database.SqlQueryRaw<AnnouncementDetailsUserViewModel>(sqlStatement, new SqlParameter("@contentId", contentId)).FirstOrDefault();

            if (announcement is null)
            {
                throw new HttpCustomException(ContentErrorCodes.InvalidContent, HttpStatusCode.NotFound);
            }

            announcement.authorName = (announcement.invitationPending == null || announcement.invitationPending == true)
                ? null
                : announcement.authorName;

            announcement.authorEmail = (announcement.invitationPending == null || announcement.invitationPending == true)
                ? announcement.authorEmail
                : null;

            result.announcement = announcement;
            #endregion

            #region subscribers
            sqlStatement = @"
                            SELECT 
	                            cs.contentUuid,
	                            cs.userId,
	                            u.name,
                                u.profilePic,
                                cs.rsvp,
                                cs.rsvpDate,
                                cs.isRead,
                                cs.readDate
                            FROM [ContentSubscriber] cs 
                            INNER JOIN [User] u 
                            ON (cs.userId = u.userId) 
                            WHERE cs.contentUuid = @contentId";

            var subscribers = _md.Database.SqlQueryRaw<SubscriberViewModel>(sqlStatement, new SqlParameter("@contentId", contentId)).ToList();

            result.subscribers = subscribers ?? new List<SubscriberViewModel>();
            #endregion

            #region owners
            sqlStatement = @"
                            SELECT 
	                            u.userId,
                                u.name,
                                u.profilePic,
                                co.contentUuid
                            FROM [ContentOwner] co 
                            INNER JOIN [User] u 
                            ON (co.userId = u.userId) 
                            WHERE co.contentUuid = @contentId";

            var owners = _md.Database.SqlQueryRaw<ContentOwnerViewModel>(sqlStatement, new SqlParameter("@contentId", contentId)).ToList();

            result.owners = owners ?? new List<ContentOwnerViewModel>();
            #endregion

            #region attachments
            sqlStatement = @"
                            SELECT 
	                            ca.contentUuid,
                                ca.contentAttachmentId,
                                ca.attachmentId,
                                ca.deleted,
                                ca.locked,
                                ca.itemOrder,
                                ca.importedFrom 
                            FROM [ContentAttachment] ca 
                            WHERE ca.contentUuid = @contentId";

            var attachments = _md.Database.SqlQueryRaw<AttachmentsViewModel>(sqlStatement, new SqlParameter("@contentId", contentId)).ToList();

            result.attachments = attachments ?? new List<AttachmentsViewModel>();
            #endregion

            var workgroupService = new WorkgroupService(_authUtil.UserId);
            var workgroup = await workgroupService.Get(announcement.workgroupId, includeHomeData: false);
            var boardOwners = workgroup.WorkgroupOwner;

            result.isWorkgroupOwner = boardOwners.Any(o => o.userId == _authUtil.UserId);
            
            return result;
        }


        private async Task<List<AnnouncementViewModel>> GetFilteredAnnouncements(AnnouncementRequestFilter filters)
        {
            string sql_tasks_vm = @"SELECT	C.contentId, 
		                                    C.contentUuid,
                                            C.title AS title,
		                                    C.createDate, 

		                                    C.createUser, 
                                            U_C.[name] as createUserName, 
                                            U_C.profilePic as createUserPic, 

		                                    C.[status] , 
		                                    C.workgroupId, 
		                                    W.[name] as workgroupName, 
		                                    W.bulletColor as workgroupColor,
		                                    CL.[clientId] as clientId, 
                                            CL.[name] as clientName,

                                            A.announcementId,
                                            A.body,
		                                    A.[type],
                                            A.systemGenerated,
                                            A.originalActivityId,
                                            A.originalContentId,
                                            A.contentUuid as originalContentUuid,
                                            A.originalContentType,
                                            CS.isRead,
                                            CS.readDate
		                                   
                                    from Content C
                                    inner join ContentPermission CP ON C.contentId = CP.contentId
                                    inner join Announcement A ON C.contentId = A.contentId
                                    inner join [User] U_C ON C.createUser = U_C.userId
                                    inner join [Workgroup] W ON C.workgroupId = W.workgroupId
                                    inner join [Client] CL ON W.clientId = CL.clientId 
                                    left join ContentSubscriber CS ON CS.contentId = C.contentId AND CS.userId = @userId
                                    WHERE 
                                        C.deleted <> 1 AND CP.userId = @userId
                                    AND C.type = 'Announcement'
                                    AND CL.deleted <> 1 AND CL.blocked <> 1";

            sql_tasks_vm += "  AND CP.userId = " + _currentUser;

            if (filters.workgroups != null && filters.workgroups.Any())
            {
                sql_tasks_vm += " AND C.workgroupId IN (";

                for (int i = 0; i < filters.workgroups.Length; i++)
                {
                    sql_tasks_vm += filters.workgroups[i].ToString();

                    if (i != (filters.workgroups.Length - 1))
                    {
                        sql_tasks_vm += ", ";
                    }
                }
                sql_tasks_vm += ") ";
            }
            else
            {
                sql_tasks_vm += " AND W.archived <> 1";
            }

            if (filters.multiStatus != null && filters.multiStatus.Length > 0)
            {
                sql_tasks_vm += " AND C.status IN (";

                for (int i = 0; i < filters.multiStatus.Length; i++)
                {
                    string status_to_add = AntiSqlInjection.ValidateSqlValue(filters.multiStatus[i].ToString());
                    sql_tasks_vm += "'" + status_to_add + "'";

                    if (i != (filters.multiStatus.Length - 1))
                    {
                        sql_tasks_vm += ", ";
                    }
                }
                sql_tasks_vm += ") ";
            }

            if (filters.parentContentId.HasValue)
            {
                sql_tasks_vm += " AND C.parentContentId = " + filters.parentContentId.ToString();
            }

            if (filters.createUserId.HasValue)
            {
                sql_tasks_vm += " AND C.createUser = " + filters.createUserId.ToString();
            }
            if (!string.IsNullOrWhiteSpace(filters.status))
            {
                sql_tasks_vm += " AND C.status LIKE '" + AntiSqlInjection.ValidateSqlValue(filters.status.ToString()) + "'";
            }
            if (!string.IsNullOrWhiteSpace(filters.exceptStatus))
            {
                sql_tasks_vm += " AND C.status NOT LIKE '" + AntiSqlInjection.ValidateSqlValue(filters.exceptStatus.ToString()) + "'";
            }

            if (filters.createDateMin.HasValue)
            {
                sql_tasks_vm += " AND C.createDate >= '" + filters.createDateMin.Value.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            }

            if (filters.createDateMax.HasValue)
            {
                sql_tasks_vm += " AND C.createDate <= '" + filters.createDateMax.Value.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            }

            if (filters.isRead.HasValue)
            {
                if (filters.isRead.Value)
                {
                    sql_tasks_vm += " AND CS.isRead = 1";
                }
                else
                {
                    sql_tasks_vm += " AND (CS.isRead = 0 OR CS.isRead IS NULL)";
                }
            }

            sql_tasks_vm += " ORDER BY C.createDate desc";

            AtlasModelCore _md = new AtlasModelCore();
            var result = _md.Database.SqlQueryRaw<AnnouncementViewModel>(sql_tasks_vm, new SqlParameter("@userId", _currentUser)).ToList();

            // 1) Extract the parent IDs we need to fetch
            var parentIds = result
                .Where(i => i.originalContentId.HasValue && i.systemGenerated)
                .Select(i => i.originalContentId.Value)
                .Distinct()
                .ToList();

            // 2) Fetch ALL parent content in a single query
            var parents = _md.Content
                .Where(c => parentIds.Contains(c.contentId))
                .Select(c => new { c.contentId, c.title })
                .ToList();

            // 3) Build an in-memory dictionary
            var parentTitles = parents
                .ToDictionary(p => p.contentId, p => p.title);

            // 4) Set bodyPreview and fill parentContentTitle from the dictionary
            foreach (var item in result)
            {
                string htmlFreeText = CrossCutting.HtmlFreeText
                    .HtmlToPlainText(item.body)
                    .Replace("\n", " ");
                item.bodyPreview = htmlFreeText;

                if (item.originalContentId.HasValue && item.systemGenerated &&
                    parentTitles.TryGetValue(item.originalContentId.Value, out var title))
                {
                    item.parentContentTitle = title;
                }
            }

            //foreach (var item in result)
            //{
            //    //todo: htmlFreeText
            //    string htmlFreeText = CrossCutting.HtmlFreeText.HtmlToPlainText(item.body).Replace("\n", " ");
            //    item.bodyPreview = htmlFreeText;

            //    if (item.originalContentId != null && item.systemGenerated)
            //    {
            //        var parent = _md.Content.Where(o => o.contentId == item.originalContentId).FirstOrDefault();
            //        item.parentContentTitle = parent.title;
            //    }
            //}

            return result;
        }


        public async Task<Tuple<int, Guid>> AddAnnouncement(int workgroupId, Content obj, int createUserId, string userAgent)
        {
            obj.type = "Announcement";

            List<Data.Entities.ContentPermission> permissions = new List<Data.Entities.ContentPermission>();
            List<Atlas.Data.Entities.ContentSubscriber> subscribers = new List<Atlas.Data.Entities.ContentSubscriber>();
            List<ContentActivity> activities = new List<ContentActivity>();
            List<Atlas.Data.Entities.ContentOwner> contentOwner = new List<Atlas.Data.Entities.ContentOwner>();

            var parentContent = new Content();
            bool generateRandom = false;
            int generateRandom_qtd = 0;

            // Valida tamanho do title do content
            if ((obj.title != null && obj.title.Length > 10000)
                || (obj.type == ContentTypes.Announcement && string.IsNullOrWhiteSpace(obj.title)))
            {
                throw new ArgumentNullException("ERROR_TITLE");
            }

            // Check Workgroup deletion
            WorkgroupService ws = new WorkgroupService(_currentUser);
            var ws1 = await ws.Get(workgroupId, includeHomeData: false);
            if (ws1.archived)
            {
                throw new Exception("BOARD ARCHIVED");
            }

            WorkgroupRepository _w_repo = new WorkgroupRepository(_currentUser);
            var lista_users_workgroup = _w_repo.GetAllUsersFromWorkgroup(workgroupId);
            var boardOwners = ws1.WorkgroupOwner;

            FeatureManagerService featureSvc = new FeatureManagerService(_currentUser);

            //----------------------------------------------
            //Check if the board quota was exceeded for the client
            //If the quota is exceeded the client's users won't be able to create any content on this client
            await featureSvc.checkPlanQuota(ws1.clientId, PlanFeatureNames.BOARD_QUOTA, false);

            //sanitização anouncement
            #region Sanitize
            var announcement = obj.Announcement.First();
            if (!announcement.systemGenerated)
            {
                var htmlParser = new HtmlParser();
                var bodyHtml = htmlParser.ParseDocument(announcement.body);
                var hasSignificantElements = bodyHtml.Body.QuerySelectorAll("img, a, video, audio, iframe, object, embed").Any();

                if (string.IsNullOrWhiteSpace(bodyHtml.DocumentElement.TextContent) && !hasSignificantElements)
                    throw new InvalidOperationException("INVALID_ANNOUNCEMENT_BODY");
            }

            Ganss.Xss.HtmlSanitizer sanitizer = new Ganss.Xss.HtmlSanitizer();
            sanitizer.AllowDataAttributes = true;
            sanitizer.AllowedSchemes.Add("data");
            sanitizer.AllowedAttributes.Add("class");
            sanitizer.AllowedAttributes.Add("src");

            // Clean up the unexpected HTML tags
            var invalidTags = new string[] { "form", "button", "div" };
            sanitizer.AllowedTags.ExceptWith(invalidTags);

            sanitizer.RemovingAttribute += (x, e) => e.Cancel = e.Reason == RemoveReason.NotAllowedUrlValue
                && e.Attribute.Value.Length >= 0xfff0
                && e.Attribute.Value.StartsWith("data:", StringComparison.OrdinalIgnoreCase);

            sanitizer.PostProcessNode += (sender, e) =>
            {
                if (e.Node is AngleSharp.Html.Dom.IHtmlAnchorElement a)
                {
                    a.RemoveAttribute("style");
                }
            };

            var sanitized_text = sanitizer.Sanitize(announcement.body);

            announcement.body = sanitized_text;

            sanitizer.AllowedTags.Clear();
            obj.title = Regex.Replace(sanitizer.Sanitize(obj.title), @"[^\w\s]", "");
            #endregion

            var an = obj.Announcement.FirstOrDefault();

            if (an.systemGenerated)
            {
                if (an.originalContentId != null)
                {
                    parentContent = await _repo.Get((int)an.originalContentId);

                    if (parentContent.type == ContentTypes.Meeting && parentContent.workgroupId != workgroupId)
                    {
                        throw new SecurityException("Unauthorized attempt to create a content that does not belongs to the Board.");
                    }

                    //Só o owner da reunião pode criar um announcement automático, e o owner da reunião pode não ser do board
                    if (parentContent.UAC.changeStatus == false)
                    {
                        throw new SecurityException("Unauthorized attempt to create a content that does not belongs to the Board.");
                    }
                }
                else
                {
                    throw new Exception("ParentContentId required.");
                }
            }
            else
            {
                //Só o owner do board pode criar announcement manual
                if (!ws1.UAC.create_announcement)
                {
                    throw new SecurityException("Unauthorized attempt to create announcement");
                }
            }

            an = obj.Announcement.FirstOrDefault();

            if (an.systemGenerated)
            {
                var parentPermissions = parentContent.ContentPermission.Where(o => o.contentId == obj.parentContentId).ToList();

                var allAgendas = parentContent.Child_Content.Where(o => o.type == ContentTypes.MeetingAgendaItem && o.deleted != true).ToList();
                var allAgendas_permissions = new List<Data.Entities.ContentPermission>();
                List<Data.Entities.ContentPermission> listUsers = new List<Data.Entities.ContentPermission>();

                listUsers = parentContent.ContentPermission.ToList();

                allAgendas_permissions = _repo.GetAllForContents(listUsers.Select(o => o.userId).ToList(), allAgendas.Select(o => o.contentId).ToList());

                foreach (var user in listUsers)
                {
                    //Get all agenda items where the current user has permission
                    var allAgendas_permissions_user = allAgendas_permissions.Where(o => o.userId == user.userId).ToList();


                    //select all agendas remaining agendas the loop user DONT HAVE permissions
                    var agendas_without_permissions = allAgendas.Where(o => !allAgendas_permissions_user.Select(a => a.contentId).Contains(o.contentId));


                    //if = 0, this user have access to all agendas, so it should have access to the minute
                    if (!agendas_without_permissions.Any() && (an.type == "MINUTE_ATTACHMENT" || an.type == "MINUTE_TEXT"))
                    {
                        permissions.Add(new Data.Entities.ContentPermission()
                        {
                            createUser = _currentUser,
                            userId = user.userId,
                            allowed = true,
                            createDate = DateTime.UtcNow
                        });
                    }
                    else if (an.type == "MEETING_READY")
                    {
                        permissions.Add(new Data.Entities.ContentPermission()
                        {
                            createUser = _currentUser,
                            userId = user.userId,
                            allowed = true,
                            createDate = DateTime.UtcNow
                        });
                    }
                }
            }
            else
            {
                lista_users_workgroup.ForEach((x) =>
                {
                    permissions.Add(new Data.Entities.ContentPermission() { createUser = _currentUser, userId = x.userId, allowed = true, createDate = DateTime.UtcNow });
                });
            }

            contentOwner.Add(new Atlas.Data.Entities.ContentOwner()
            {
                userId = _currentUser,
                createUser = _currentUser,
                createDate = DateTime.UtcNow
            });

            an = obj.Announcement.FirstOrDefault();

            if (an.type == "MINUTE_ATTACHMENT" || an.type == "MINUTE_TEXT")
            {
                if (obj.parentContentId.HasValue)
                {
                    var subs_meeting = _repo.List_Subscribers(obj.parentContentId.Value);
                    subs_meeting.ForEach(o =>
                    {
                        var hasPermission = permissions.FirstOrDefault(p => p.userId == o.userId);

                        //se tem permissão no announcement é adicionado como subscriber
                        if (hasPermission != null)
                        {
                            subscribers.Add(new Atlas.Data.Entities.ContentSubscriber()
                            {
                                userId = o.userId,
                                createDate = DateTime.UtcNow
                            });
                        }
                    });
                }
            }
            else
            {
                //Se a lsita de subscribers já veio preenchida do frontend eu só adiciono como subscriber todos dessa lista que estão no board
                if (obj.ContentSubscriber.Any())
                {
                    subscribers.AddRange(obj.ContentSubscriber.Where(o => lista_users_workgroup.Select(a => a.userId).Contains(o.userId)));
                }
                //Caso a lista de subscribers não tenha sido preenchida no frontend eu verifico se o currentUser está no board,
                //caso esteja eu adiciono somente ele como subscriber, caso não esteja eu lanço uma exceção
                else
                {
                    if (lista_users_workgroup.Select(a => a.userId).Contains(_currentUser))
                    {
                        subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = _currentUser, createDate = DateTime.UtcNow });
                    }
                    else
                    {
                        throw new SecurityException("The create user cannot create an announcement on this workgroup");
                    }
                }
            }

            if (obj.ContentAttachment != null)
            {
                if (obj.ContentAttachment.Count() != 1)
                {
                    obj.ContentAttachment = new List<ContentAttachment>();
                }
            }

            //check if types contains the respective object arrays (ex. Content object type=Task, should have the Task object array filled)
            if (Helpers.ContentChecker.Check(obj))
            {
                Content res_repo = null;

                var titleSanitized = obj.Meeting.FirstOrDefault()?.title ?? obj.MeetingAgendaItem.FirstOrDefault()?.title ?? obj.title;
                if (!string.IsNullOrEmpty(titleSanitized))
                {
                    titleSanitized = Regex.Replace(titleSanitized, @"[<>]", "");
                    obj.title = titleSanitized;
                }

                try
                {
                    res_repo = _repo.Add(workgroupId, obj, permissions, subscribers, contentOwner, activities);
                }
                catch (Exception ex)
                {
                    var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                    var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage("Error while creating content"));
                    sentryEvent.Extra = new
                    {
                        ex,
                        obj,
                        permissions,
                        subscribers,
                        contentOwner,
                        activities
                    };
                    ravenClient.Capture(sentryEvent);

                    return new Tuple<int, Guid>(-1, Guid.Empty);
                }

                //if success Add()
                if (res_repo != null)
                {
                    var title = obj.title;

                    //todo: mover para business
                    activities = new List<ContentActivity>();

                    //S-32 - For announcements of type 'systemGenerated', use the createDate of the previous contentActivity that triggered the autoGen
                    var createDate = DateTime.UtcNow;

                    an = obj.Announcement.FirstOrDefault();

                    if (an.systemGenerated && obj.createDate != null)
                    {
                        createDate = obj.createDate;
                    }

                    string contentData = JsonConvert.SerializeObject(new
                    {
                        title = title
                    });
                    bool processed = false;
                    string subItemType = "";

                    new ContentActivityService(_currentUser).Add(res_repo.contentId, res_repo.contentUuid, new ContentActivity()
                    {
                        date = createDate,
                        type = "CREATED",
                        activityUser = _currentUser,
                        contentData = contentData,
                        subItemType = subItemType,
                        processed = processed,
                    }, userAgent);
                }

                return new Tuple<int, Guid>(res_repo.contentId, res_repo.contentUuid);
            }
            else
            {
                return new Tuple<int, Guid>(-1, Guid.Empty);
            }
        }
    }
}
