using Atlas.Data.Entities;
using Atlas.Data.Repository;
using PdfSharpCore.Drawing;
using PdfSharpCore.Pdf;
using PdfSharpCore.Pdf.IO;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

namespace Atlas.Business.Helpers
{
    public class PDFGenerator_v3
    {
        private List<Pagination> _pagination;
        User _currentUser;
        public MergeResult MergeResult = null;
        ContentRepository _repo;
        ContentService _service;

        readonly string EnvironmentUrl = ConfigurationManager.AppSettings["EnvironmentUrl"] ?? Environment.GetEnvironmentVariable("EnvironmentUrl") ?? "https://www.atlasgov.com";

        public PDFGenerator_v3(User currentUser)
        {
            _currentUser = currentUser;
            _repo = new ContentRepository(_currentUser.userId);
            _service = new ContentService(_currentUser.userId);
        }

        public byte[] WriteWatermarkAllPages(byte[] source, string bkg_text, string bkg_text_email, bool paging = true, bool enableBackgroundWatermark = true)
        {
            using var inputStream = new MemoryStream(source);
            using var inputDocument = PdfReader.Open(inputStream, PdfDocumentOpenMode.Import);

            using var outputStream = new MemoryStream();
            var outputDocument = new PdfSharpCore.Pdf.PdfDocument();

            int times = inputDocument.PageCount;

            for (int i = 0; i < times; i++)
            {
                var page = inputDocument.Pages[i];
                outputDocument.AddPage(page);

                using var gfx = XGraphics.FromPdfPage(outputDocument.Pages[i]);

                if (enableBackgroundWatermark)
                {
                    var font = new XFont("Helvetica", 28, XFontStyle.Regular);
                    AddWaterMark(gfx, bkg_text, bkg_text_email, font, 28, 35, XColors.Blue, page, page.MediaBox.ToXRect());
                }

                if (paging)
                {
                    AddPagingBox(gfx, $"{i + 1}/{times}", times, page.MediaBox.ToXRect());
                }
            }

            outputDocument.Save(outputStream);
            return outputStream.ToArray();
        }

        private static void AddWaterMark(
            XGraphics gfx,
            string textLine1,
            string textLine2,
            XFont font,
            double fontSize,
            double angle,
            XColor color,
            PdfPage page,
            XRect? rect = null)
        {
            // Define área onde a marca d'água será desenhada
            var ps = rect ?? new XRect(0, 0, page.Width, page.Height);

            // Calcula centro do retângulo
            double x = ps.X + ps.Width / 2;
            double y = ps.Y + ps.Height / 2;

            // Cria brush com opacidade (alpha de 0 a 255, aqui 25 é ~10%)
            var transparentColor = XColor.FromArgb(25, color.R, color.G, color.B);
            var brush = new XSolidBrush(transparentColor);

            // Salva o estado gráfico para restaurar depois
            gfx.Save();

            // Move o sistema de coordenadas para o centro onde a marca será desenhada
            gfx.TranslateTransform(x, y);

            // Rotaciona a página
            gfx.RotateTransform(angle);

            // Define alinhamento centralizado (texto desenhado na origem)
            var format = new XStringFormat
            {
                Alignment = XStringAlignment.Center,
                LineAlignment = XLineAlignment.Center
            };

            // Desenha as duas linhas da marca d'água com deslocamento vertical
            gfx.DrawString(textLine1, font, brush, new XPoint(0, 0), format);
            gfx.DrawString(textLine2, font, brush, new XPoint(30, 30), format);

            // Restaura estado gráfico
            gfx.Restore();
        }

        private void AddPagingBox(XGraphics gfx, string count, int numberPages, XRect pageRect)
        {
            double initialBoxX = pageRect.Right;

            if (numberPages <= 99)
                initialBoxX -= 49;
            else if (numberPages <= 999)
                initialBoxX -= 70;
            else if (numberPages < 9999)
                initialBoxX -= 90;
            else
                initialBoxX -= 100;

            double initialBoxY = pageRect.Top - (pageRect.Height / 5);

            var boxWidth = numberPages > 9999 ? 120 : (numberPages > 999 ? 110 : (numberPages > 99 ? 90 : 70));
            var boxHeight = 30;

            var rect = new XRect(initialBoxX - 20, initialBoxY, boxWidth, boxHeight);
            var boxColor = XColor.FromArgb(128, 63, 207, 178); // 50% opacity
            var pen = new XPen(boxColor);
            var brush = new XSolidBrush(boxColor);

            gfx.DrawRectangle(pen, brush, rect);

            // Texto (numeração)
            var font = new XFont("Helvetica", 14, XFontStyle.Regular);
            var textColor = XColor.FromArgb(179, 254, 254, 254); // ~70% opacity
            var textBrush = new XSolidBrush(textColor);

            double offsetX = numberPages <= 99 ? 25 :
                             numberPages <= 999 ? 33 :
                             numberPages <= 9999 ? 40 : 50;

            gfx.DrawString(count, font, textBrush, new XPoint(initialBoxX + offsetX, initialBoxY + 20), XStringFormats.Center);
            
            byte[] logoPath = GetDefaultAtlasLogoWhite();

            using Stream imageStream = ByteArrayToStream(logoPath);
            using XImage image = XImage.FromStream(() => imageStream);

            gfx.DrawImage(image, initialBoxX - 15, initialBoxY + 5, 20, 20);
        }

        private static Stream ByteArrayToStream(byte[] bytes)
        {
            return new MemoryStream(bytes ?? Array.Empty<byte>());
        }

        private byte[] GetDefaultAtlasLogoWhite()
        {
            // var converter = new System.Drawing.ImageConverter();
            // logo_array = (byte[])converter.ConvertTo(Atlas.Business.Resources.ReportResource.logo_white, typeof(byte[]));
            byte[] logo_array = ImageToByteArray(Atlas.Business.Resources.ReportResource.logo_white);

            return logo_array;
        }

        public byte[] ImageToByteArray(Bitmap image)
        {
            using var ms = new MemoryStream();
            image.Save(ms, ImageFormat.Bmp); // ou outro formato desejado
            return ms.ToArray();
        }
    }
}

